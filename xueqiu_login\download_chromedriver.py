"""
自动下载ChromeDriver
"""

import requests
import zipfile
import io
import os
import subprocess

def get_chrome_version():
    """获取Chrome浏览器版本"""
    try:
        # Windows注册表方式获取Chrome版本
        result = subprocess.run(
            ['reg', 'query', 'HKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon', '/v', 'version'],
            capture_output=True, text=True, timeout=10
        )
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'version' in line.lower():
                    version = line.split()[-1]
                    return version.split('.')[0]  # 返回主版本号
    except:
        pass
    
    try:
        # 尝试直接运行chrome获取版本
        result = subprocess.run(
            ['chrome', '--version'],
            capture_output=True, text=True, timeout=5
        )
        if result.returncode == 0:
            version = result.stdout.strip().split()[-1]
            return version.split('.')[0]
    except:
        pass
    
    return "120"  # 默认版本

def download_chromedriver():
    """下载匹配的ChromeDriver"""
    try:
        major_version = get_chrome_version()
        print(f"检测到Chrome主版本: {major_version}")
        
        # 尝试下载对应版本的ChromeDriver
        versions_to_try = [major_version, str(int(major_version)-1), str(int(major_version)-2)]
        
        for version in versions_to_try:
            url = f"https://chromedriver.storage.googleapis.com/{version}.0.0.0/chromedriver_win32.zip"
            print(f"尝试下载版本 {version}: {url}")
            
            try:
                response = requests.get(url, timeout=30)
                if response.status_code == 200:
                    # 解压到当前目录
                    with zipfile.ZipFile(io.BytesIO(response.content)) as zip_file:
                        zip_file.extractall(".")
                    print("ChromeDriver下载并解压成功")
                    return True
            except Exception as e:
                print(f"版本 {version} 下载失败: {e}")
                continue
        
        print("所有版本尝试失败")
        return False
        
    except Exception as e:
        print(f"下载过程错误: {e}")
        return False

if __name__ == "__main__":
    if download_chromedriver():
        print("ChromeDriver配置完成")
    else:
        print("自动下载失败，请手动下载: https://chromedriver.chromium.org/")