Traceback (most recent call last):
  File "d:\myCursor\XDB3\xueqiu_gui_scraper\gui_scraper.py", line 2237, in main
    app = XueqiuGUI()
  File "d:\myCursor\XDB3\xueqiu_gui_scraper\gui_scraper.py", line 1777, in __init__
    self.setup_browser()
    ^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\tkinter\__init__.py", line 2546, in __getattr__
    return getattr(self.tk, attr)
AttributeError: '_tkinter.tkapp' object has no attribute 'setup_browser'
