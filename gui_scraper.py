"""
GUI Xueqiu Web Scraper with Tabbed Interface
优化版本 - 修复界面卡顿问题
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import json
import logging
from dataclasses import dataclass, asdict
from typing import List, Optional, Dict, Any, Callable
from enum import Enum, auto
from abc import ABC, abstractmethod
import re
from datetime import datetime
import os
import queue
from concurrent.futures import ThreadPoolExecutor, Future

# Selenium imports
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xueqiu_gui_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ==================== ENUMS AND DATA CLASSES ====================

class ScraperState(Enum):
    INITIALIZED = auto()
    LOGIN_REQUIRED = auto()
    LOGGED_IN = auto()
    SCRAPING = auto()
    COMPLETED = auto()
    ERROR = auto()
    RUNNING = auto()
    PAUSED = auto()
    STOPPED = auto()

class BrowserType(Enum):
    CHROME = auto()
    FIREFOX = auto()
    EDGE = auto()

@dataclass
class ScraperConfig:
    """Configuration for the scraper"""
    chrome_driver_path: str = r'd:/myCursor/XDB3/chromedriver/chromedriver.exe'
    max_scrolls: int = 20
    scroll_interval: float = 1.5
    max_no_new_content: int = 3
    wait_timeout: int = 20
    headless: bool = False
    user_data_dir: str = "chrome_profile"
    browser_type: BrowserType = BrowserType.CHROME
    max_concurrent_tabs: int = 5

# 修改 BlogPost 类的字段顺序
@dataclass
class BlogPost:
    """Data class for blog post information"""
    index: int
    author_name: str
    user_id: str
    profile_url: str
    publish_time: str
    title: str
    content: str
    likes: int
    comments: int
    reposts: int
    has_images: bool  # 没有默认值的字段必须放在前面
    favorites: int = 0  # 新增收藏数（有默认值）
    is_favorited: bool = False  # 新增是否收藏（有默认值）
    image_count: int = 0
    search_keyword: str = ""
    publish_source: str = "未知来源"
    image_urls: List[str] = None
    post_id: str = "未知"
    
    def __post_init__(self):
        if self.image_urls is None:
            self.image_urls = []
    
    def to_dict(self):
        return asdict(self)

# ==================== THREAD-SAFE MESSAGE QUEUE ====================

# 修改 MessageQueue 类，使其支持每个标签页独立的消息处理
class TabMessageQueue:
    """每个标签页独立的消息队列"""
    def __init__(self, tab_id, gui_callback):
        self.tab_id = tab_id
        self.gui_callback = gui_callback
        self.queue = queue.Queue()
    
    def put_message(self, message_type: str, data: Any):
        """将消息放入队列"""
        self.queue.put((message_type, data))
    
    def process_messages(self):
        """处理当前标签页的消息"""
        processed_count = 0
        while not self.queue.empty() and processed_count < 20:
            try:
                message_type, data = self.queue.get_nowait()
                self.gui_callback(self.tab_id, message_type, data)
                self.queue.task_done()
                processed_count += 1
            except queue.Empty:
                break
        return not self.queue.empty()

# ==================== STRATEGY PATTERN FOR CONTENT EXTRACTION ====================

class ContentExtractionStrategy(ABC):
    """Abstract base class for content extraction strategies"""
    
    @abstractmethod
    def extract(self, element, driver) -> Any:
        pass

class TitleExtractionStrategy(ContentExtractionStrategy):
    """Strategy for extracting titles"""
    
    def extract(self, item, driver) -> str:
        title_selectors = [
            ".timeline__item__title",
            ".title",
            ".item-title",
            "h1", "h2", "h3", "h4",
            "[class*='title']"
        ]
        
        for selector in title_selectors:
            try:
                title_elem = item.find_element(By.CSS_SELECTOR, selector)
                title_text = title_elem.text.strip()
                if title_text and title_text != "专栏" and len(title_text) > 2:
                    return title_text
            except:
                continue
        
        return "无标题"

class ContentExtractionStrategyImpl(ContentExtractionStrategy):
    """Strategy for extracting content"""
    
    def extract(self, item, driver) -> str:
        content = "无内容"
        
        try:
            # 首先检查是否有展开的内容
            expanded_content = self._check_expanded_content(item)
            if expanded_content and expanded_content != "无内容":
                return expanded_content
            
            # 尝试多种内容选择器
            content_selectors = [
                ".content--description",
                ".timeline__item__content",
                ".content",
                "[class*='content']"
            ]
            
            for selector in content_selectors:
                try:
                    content_elems = item.find_elements(By.CSS_SELECTOR, selector)
                    for elem in content_elems:
                        text = elem.text.strip()
                        if text and len(text) > 10:
                            content = text
                            break
                    if content != "无内容":
                        break
                except:
                    continue
            
            # 如果还是没有内容，尝试提取整个项目文本
            if content == "无内容":
                full_text = item.text.strip()
                lines = full_text.split('\n')
                content_lines = []
                for line in lines:
                    line = line.strip()
                    if (line and 
                        not any(keyword in line for keyword in ['转发', '评论', '赞', '收藏', '投诉']) and
                        not line.startswith('@') and
                        len(line) > 5):
                        content_lines.append(line)
                
                if content_lines:
                    content = '\n'.join(content_lines)
                    
        except Exception as e:
            logger.error(f"内容提取错误: {e}")
        
        return content
    
    def _check_expanded_content(self, item):
        """检查是否有展开的内容"""
        try:
            expanded_selectors = [
                ".timeline__item__content--expanded",
                ".content--expanded",
                "[class*='expanded']",
                "[class*='unfold']"
            ]
            
            for selector in expanded_selectors:
                try:
                    expanded_elems = item.find_elements(By.CSS_SELECTOR, selector)
                    for elem in expanded_elems:
                        if elem.is_displayed():
                            text = elem.text.strip()
                            if text and len(text) > 10:
                                return text
                except:
                    continue
                    
            return "无内容"
        except:
            return "无内容"

class ImageExtractionStrategy(ContentExtractionStrategy):
    """Strategy for extracting image information"""
    
    def extract(self, item, driver) -> tuple:
        has_images = False
        image_count = 0
        image_urls = []
        
        try:
            image_selectors = [
                ".timeline__item__image",
                ".image-container",
                ".images",
                "[class*='image']",
                "img"
            ]
            
            for selector in image_selectors:
                try:
                    images = item.find_elements(By.CSS_SELECTOR, selector)
                    for img in images:
                        if img.is_displayed():
                            has_images = True
                            image_count += 1
                            
                            img_url = img.get_attribute("src") or img.get_attribute("data-src")
                            if img_url and img_url not in image_urls:
                                image_urls.append(img_url)
                                
                except:
                    continue
                    
        except Exception as e:
            logger.error(f"提取图片信息失败: {e}")
        
        return has_images, image_count, image_urls

# 修改 EngagementExtractionStrategy 类来支持收藏信息提取
class EngagementExtractionStrategy(ContentExtractionStrategy):
    """Strategy for extracting engagement metrics including favorites"""
    
    def extract(self, item, driver) -> tuple:
        likes = comments = reposts = favorites = 0
        is_favorited = False
        
        try:
            # 使用精确的选择器定位互动区域
            footer_selectors = [
                ".timeline__item__ft",
                ".timeline__item__ft--other",
                ".item-actions",
                ".actions"
            ]
            
            for footer_selector in footer_selectors:
                try:
                    footer = item.find_element(By.CSS_SELECTOR, footer_selector)
                    
                    # 查找所有的互动控制元素
                    control_elements = footer.find_elements(By.CSS_SELECTOR, ".timeline__item__control")
                    
                    # 按照顺序提取互动数据
                    for i, control in enumerate(control_elements):
                        try:
                            # 获取span元素中的文本（包含数字）
                            span_elem = control.find_element(By.CSS_SELECTOR, "span")
                            count_text = span_elem.text.strip()
                            
                            # 获取图标用于判断类型
                            icon_elem = control.find_element(By.CSS_SELECTOR, "i.iconfont")
                            icon_html = icon_elem.get_attribute("innerHTML") or ""
                            
                            # 根据图标和顺序判断互动类型
                            if i == 0 or "" in icon_html:  # 转发
                                reposts = self._parse_engagement_number(count_text)
                            elif i == 1 or "" in icon_html:  # 评论
                                comments = self._parse_engagement_number(count_text)
                            elif i == 2 or "" in icon_html:  # 点赞
                                likes = self._parse_engagement_number(count_text)
                            elif i == 3:  # 收藏（第4个元素）
                                # 检查是否是收藏按钮
                                if "" in icon_html or "" in icon_html:
                                    favorites = self._parse_engagement_number(count_text)
                                    
                                    # 检查是否已收藏
                                    if "active" in control.get_attribute("class") or "取消收藏" in span_elem.text:
                                        is_favorited = True
                                
                        except Exception as e:
                            continue
                    
                    # 如果找到了数据就跳出循环
                    if likes > 0 or comments > 0 or reposts > 0 or favorites > 0:
                        break
                        
                except Exception as e:
                    continue
            
            # 如果还是没有找到，使用备用方法
            if likes == 0 and comments == 0 and reposts == 0 and favorites == 0:
                likes, comments, reposts, favorites, is_favorited = self._fallback_engagement_extraction(item.text)
                
        except Exception as e:
            logger.error(f"提取互动数据失败: {e}")
            likes, comments, reposts, favorites, is_favorited = self._fallback_engagement_extraction(item.text)
        
        return likes, comments, reposts, favorites, is_favorited
    
    def _fallback_engagement_extraction(self, text: str) -> tuple:
        """备用互动数据提取方法"""
        likes = comments = reposts = favorites = 0
        is_favorited = False
        
        patterns = {
            'likes': [r'赞\s*(\d+[KkMm万]*)', r'(\d+[KkMm万]*)\s*赞'],
            'comments': [r'评论\s*(\d+[KkMm万]*)', r'(\d+[KkMm万]*)\s*评论'],
            'reposts': [r'转发\s*(\d+[KkMm万]*)', r'(\d+[KkMm万]*)\s*转发'],
            'favorites': [r'收藏\s*(\d+[KkMm万]*)', r'(\d+[KkMm万]*)\s*收藏']
        }
        
        for metric, pattern_list in patterns.items():
            for pattern in pattern_list:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    value = self._parse_engagement_number(match.group(1))
                    if metric == 'likes':
                        likes = value
                    elif metric == 'comments':
                        comments = value
                    elif metric == 'reposts':
                        reposts = value
                    elif metric == 'favorites':
                        favorites = value
                    break
        
        # 检查是否已收藏
        if "取消收藏" in text:
            is_favorited = True
        elif "收藏" in text and "取消收藏" not in text:
            is_favorited = False
        
        return likes, comments, reposts, favorites, is_favorited
    
    def _parse_engagement_number(self, text: str) -> int:
        """Parse engagement numbers that might contain K/M suffixes or Chinese text"""
        if not text or text in ["转发", "评论", "赞", "收藏", "讨论", "投诉"]:
            return 0
        
        try:
            # 移除可能的空格和特殊字符
            text = text.strip().replace(' ', '').replace(',', '')
            
            # 处理中文数字后缀
            if 'K' in text or 'k' in text:
                return int(float(text.replace('K', '').replace('k', '')) * 1000)
            elif 'M' in text or 'm' in text:
                return int(float(text.replace('M', '').replace('m', '')) * 1000000)
            elif '万' in text:
                return int(float(text.replace('万', '')) * 10000)
            else:
                # 尝试直接转换为整数
                return int(text)
        except:
            return 0
    
    def _extract_engagement_fallback(self, item):
        """备用方法：通过文本分析提取互动数据"""
        likes = comments = reposts = 0
        
        try:
            # 获取整个项目的文本内容
            item_text = item.text
            
            # 使用正则表达式查找互动数据
            import re
            
            # 查找点赞数
            like_patterns = [
                r'赞\s*(\d+[KkMm万]*)',
                r'(\d+[KkMm万]*)\s*赞',
                r'like.*?(\d+[KkMm万]*)',
                r'(\d+[KkMm万]*).*?like'
            ]
            
            for pattern in like_patterns:
                match = re.search(pattern, item_text, re.IGNORECASE)
                if match:
                    likes = self._parse_engagement_number(match.group(1))
                    break
            
            # 查找评论数
            comment_patterns = [
                r'评论\s*(\d+[KkMm万]*)',
                r'(\d+[KkMm万]*)\s*评论',
                r'comment.*?(\d+[KkMm万]*)',
                r'(\d+[KkMm万]*).*?comment'
            ]
            
            for pattern in comment_patterns:
                match = re.search(pattern, item_text, re.IGNORECASE)
                if match:
                    comments = self._parse_engagement_number(match.group(1))
                    break
            
            # 查找转发数
            repost_patterns = [
                r'转发\s*(\d+[KkMm万]*)',
                r'(\d+[KkMm万]*)\s*转发',
                r'repost.*?(\d+[KkMm万]*)',
                r'(\d+[KkMm万]*).*?repost',
                r'分享\s*(\d+[KkMm万]*)',
                r'(\d+[KkMm万]*)\s*分享'
            ]
            
            for pattern in repost_patterns:
                match = re.search(pattern, item_text, re.IGNORECASE)
                if match:
                    reposts = self._parse_engagement_number(match.group(1))
                    break
                    
        except Exception as e:
            logger.error(f"备用方法提取互动数据失败: {e}")
        
        return likes, comments, reposts

# ==================== FACTORY PATTERN FOR BROWSER CREATION ====================

class BrowserFactory:
    """Factory for creating browser instances"""
    
    @staticmethod
    def create_browser(config: ScraperConfig) -> Optional[webdriver.Remote]:
        """Create a browser instance based on configuration"""
        try:
            if config.browser_type == BrowserType.CHROME:
                return BrowserFactory._create_chrome_browser(config)
            else:
                raise ValueError(f"Unsupported browser type: {config.browser_type}")
        except Exception as e:
            logger.error(f"Browser creation failed: {e}")
            return None
    
    @staticmethod
    def _create_chrome_browser(config: ScraperConfig) -> webdriver.Chrome:
        """Create Chrome browser instance"""
        options = Options()
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('window-size=1200x800')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument(f'--user-data-dir={config.user_data_dir}')
        
        options.add_argument('--remote-debugging-port=9222')
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-popup-blocking')
        
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-ssl-errors')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--disable-web-security')
        
        options.add_experimental_option('excludeSwitches', ['enable-logging'])
        
        if config.headless:
            options.add_argument('--headless')
        
        service = Service(config.chrome_driver_path)
        driver = webdriver.Chrome(service=service, options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver

# ==================== CONFIGURATION MANAGER ====================

class ConfigManager:
    """Manages application configuration"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.default_config = {
            "chrome_driver_path": r'D:/myCursor/XDB3/chromedriver/chromedriver-win64/chromedriver.exe',
            "max_scrolls": 20,
            "scroll_interval": 1.5,
            "max_no_new_content": 3,
            "wait_timeout": 20,
            "headless": False,
            "user_data_dir": "chrome_profile",
            "browser_type": "CHROME",
            "max_concurrent_tabs": 5
        }
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file or use defaults"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return self.default_config.copy()
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to save config: {e}")
    
    def get_scraper_config(self) -> ScraperConfig:
        """Convert to ScraperConfig object"""
        return ScraperConfig(
            chrome_driver_path=self.config["chrome_driver_path"],
            max_scrolls=self.config["max_scrolls"],
            scroll_interval=self.config["scroll_interval"],
            max_no_new_content=self.config["max_no_new_content"],
            wait_timeout=self.config["wait_timeout"],
            headless=self.config["headless"],
            user_data_dir=self.config["user_data_dir"]
        )

# ==================== MAIN SCRAPER CLASS ====================

class XueqiuScraperTab:
    def __init__(self, tab_id: int, keyword: str, max_items: int, gui_callback: Callable, config: ScraperConfig):
        self.tab_id = tab_id
        self.keyword = keyword
        self.max_items = max_items
        self.config = config
        
        # 每个标签页有自己的消息队列
        self.message_queue = TabMessageQueue(tab_id, gui_callback)
        
        self.browser = None
        self.is_logged_in = False
        self.posts_data: List[BlogPost] = []
        self.thread = None
        self.is_running = False
        self.state = ScraperState.INITIALIZED
        
        # Initialize strategies
        self.title_strategy = TitleExtractionStrategy()
        self.content_strategy = ContentExtractionStrategyImpl()
        self.image_strategy = ImageExtractionStrategy()
        self.engagement_strategy = EngagementExtractionStrategy()

        def _update_status(self, message: str):
            """通过独立的消息队列更新状态"""
            self.message_queue.put_message('status', {'message': message})
        
        def _update_progress(self, current: int, total: int):
            """更新进度"""
            self.message_queue.put_message('progress', {'current': current, 'total': total})

    def _initialize_browser(self):
        """为每个标签页初始化独立的浏览器实例 - 自动打开雪球主页"""
        try:
            options = Options()
            options.add_argument('--disable-gpu')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('window-size=1200x800')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument(f'--user-data-dir={self.config.user_data_dir}_{self.tab_id}')
            
            options.add_argument('--remote-debugging-port=9222')
            options.add_argument('--no-first-run')
            options.add_argument('--no-default-browser-check')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-popup-blocking')
            
            options.add_argument('--ignore-certificate-errors')
            options.add_argument('--ignore-ssl-errors')
            options.add_argument('--allow-running-insecure-content')
            options.add_argument('--disable-web-security')
            
            options.add_experimental_option('excludeSwitches', ['enable-logging'])
            
            service = Service(self.config.chrome_driver_path)
            self.browser = webdriver.Chrome(service=service, options=options)
            self.browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 自动打开雪球主页
            self.browser.get("https://xueqiu.com/")
            WebDriverWait(self.browser, 20).until(
                lambda d: d.execute_script('return document.readyState') == 'complete'
            )
            
            self._update_status("浏览器已初始化并打开雪球主页")
            return True
            
        except Exception as e:
            self._update_status(f"浏览器初始化失败: {e}")
            return False

    def _check_login_status(self):
        """改进的登录状态检测方法 - 更严格的检查"""
        if not self.browser:
            self.is_logged_in = False
            return False
        
        try:
            # 等待页面加载
            WebDriverWait(self.browser, 10).until(
                lambda d: d.execute_script('return document.readyState') == 'complete'
            )
            
            current_url = self.browser.current_url
            page_source = self.browser.page_source
            
            # 调试信息
            self._update_status(f"检查登录状态: {current_url}")
            
            # 1. 首先检查明确的未登录特征（优先检查）
            unlogged_indicators = [
                "/snowman/login", "/account/login", "/login",
                "newLogin_modal", "请输入手机号", "发送验证码",
                "验证码登录", "账号密码登录", "二维码登录"
            ]
            
            for indicator in unlogged_indicators:
                if indicator in current_url or indicator in page_source:
                    self.is_logged_in = False
                    self._update_status(f"检测到未登录特征: {indicator}")
                    return False
            
            # 2. 检查登录表单元素
            login_form_selectors = [
                "input[name='telephone']", "input[name='username']",
                "input[name='password']", "input[placeholder*='手机']",
                "input[placeholder*='验证码']", "input[placeholder*='密码']"
            ]
            
            for selector in login_form_selectors:
                try:
                    elements = self.browser.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            self.is_logged_in = False
                            self._update_status(f"检测到登录表单: {selector}")
                            return False
                except:
                    continue
            
            # 3. 检查明确的登录成功特征（需要多个条件同时满足）
            logged_indicators = [
                "user__col--name", "side_user_name", "xavatar.imedao.com",
                "user__col--friends", "发帖", "我的资产", "我的消息"
            ]
            
            # 需要至少2个登录特征才能确认登录
            found_indicators = 0
            for indicator in logged_indicators:
                if indicator in page_source:
                    found_indicators += 1
                    if found_indicators >= 2:  # 需要至少2个特征确认登录
                        self.is_logged_in = True
                        self._update_status(f"检测到登录特征: {indicator}")
                        return True
            
            # 4. 使用JavaScript精确检查用户元素
            try:
                # 检查用户头像（必须是雪球头像）
                avatar_elements = self.browser.execute_script("""
                    const avatars = document.querySelectorAll('.avatar');
                    let validAvatars = [];
                    for (let avatar of avatars) {
                        if (avatar.src && avatar.src.includes('xavatar.imedao.com')) {
                            validAvatars.push(avatar);
                        }
                    }
                    return validAvatars;
                """)
                
                # 检查用户名（不能是登录/注册）
                username_elements = self.browser.execute_script("""
                    const usernames = document.querySelectorAll('#side_user_name, .user__col--name');
                    let validUsernames = [];
                    for (let username of usernames) {
                        if (username.textContent && 
                            !username.textContent.includes('登录') && 
                            !username.textContent.includes('注册') &&
                            username.textContent.trim().length > 0) {
                            validUsernames.push(username);
                        }
                    }
                    return validUsernames;
                """)
                
                # 需要同时有有效头像和用户名才确认登录
                if (avatar_elements and len(avatar_elements) > 0 and 
                    username_elements and len(username_elements) > 0):
                    self.is_logged_in = True
                    self._update_status("JavaScript检测到登录状态")
                    return True
                    
            except Exception as js_error:
                self._update_status(f"JavaScript检查错误: {js_error}")
            
            # 5. 默认状态为未登录
            self.is_logged_in = False
            self._update_status("未检测到明确的登录状态")
            return False
            
        except Exception as e:
            self._update_status(f"登录状态检查失败: {e}")
            self.is_logged_in = False
            return False
    def _create_browser(self) -> bool:
        """创建独立的浏览器实例"""
        try:
            self.browser = BrowserFactory.create_browser(self.config)
            if self.browser:
                self._update_status("浏览器初始化成功")
                return True
            else:
                self._update_status("浏览器初始化失败")
                return False
        except Exception as e:
            self._update_status(f"浏览器创建错误: {e}")
            return False
        
    # 修改 XueqiuScraperTab 类的 _check_login_status 方法
    def _check_login_status(self):
        """基于提供的页面元素改进登录状态检测"""
        if not self.browser:
            self.is_logged_in = False
            return False
        
        try:
            # 等待页面加载
            WebDriverWait(self.browser, 5).until(
                lambda d: d.execute_script('return document.readyState') == 'complete'
            )
            
            current_url = self.browser.current_url
            
            # 方法1: 检查URL中的登录页面特征
            if any(x in current_url for x in ['/snowman/login', '/account/login', '/login']):
                self.is_logged_in = False
                return False
            
            # 方法2: 检查登录模态框（未登录状态）
            login_modal_selectors = [
                ".newLogin_modal__login__jj",
                ".login-modal",
                ".login-dialog",
                "[class*='login']"
            ]
            
            for selector in login_modal_selectors:
                try:
                    elements = self.browser.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            self.is_logged_in = False
                            return False
                except:
                    continue
            
            # 方法3: 检查登录表单元素（未登录状态）
            login_form_selectors = [
                "input[name='telephone']",
                "input[name='username']",
                "input[name='password']",
                "input[placeholder*='手机号']",
                "input[placeholder*='验证码']",
                "input[placeholder*='密码']"
            ]
            
            for selector in login_form_selectors:
                try:
                    elements = self.browser.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            self.is_logged_in = False
                            return False
                except:
                    continue
            
            # 方法4: 检查用户信息元素（登录状态）
            user_info_selectors = [
                "#side_user_name",  # 登录后的用户名元素
                ".user__col--name",  # 用户列名称
                ".avatar[src*='xavatar.imedao.com']",  # 用户头像
                ".user__col--friends",  # 关注/粉丝信息
                ".user__control__pannel"  # 用户控制面板
            ]
            
            for selector in user_info_selectors:
                try:
                    elements = self.browser.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # 特别检查用户名元素（不是登录/注册文本）
                            if selector in ["#side_user_name", ".user__col--name"]:
                                username = element.text.strip()
                                if username and username not in ["登录", "注册", "登录/注册"]:
                                    self.is_logged_in = True
                                    return True
                            else:
                                self.is_logged_in = True
                                return True
                except:
                    continue
            
            # 方法5: 检查菜单项（登录状态）
            menu_selectors = [
                ".menu_icon",
                ".menu_name",
                "[class*='menu']"
            ]
            
            for selector in menu_selectors:
                try:
                    elements = self.browser.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.text in ["首页", "自选", "行情", "基金", "我的资产"]:
                            self.is_logged_in = True
                            return True
                except:
                    continue
            
            # 方法6: 检查页面标题和内容
            page_title = self.browser.title.lower()
            page_html = self.browser.page_source
            
            # 登录成功的特征
            if any(x in page_html for x in ['/u/', 'user__col', 'xavatar.imedao.com']):
                self.is_logged_in = True
                return True
            
            # 未登录的特征
            if any(x in page_html for x in ['newLogin_modal', 'snowman/login', '请输入手机号']):
                self.is_logged_in = False
                return False
            
            # 默认状态为未登录
            self.is_logged_in = False
            return False
            
        except Exception as e:
            self._update_status(f"登录状态检查失败: {e}")
            self.is_logged_in = False
            return False
        
    # 修改 XueqiuScraperTab 类的 manual_login 方法
    def manual_login(self):
        """手动登录的公共方法"""
        self._update_status("开始手动登录流程...")
        return self._manual_login()
    # 修改 _manual_login 方法
    def _manual_login(self):
        """执行手动登录的内部方法 - 优化版本"""
        if not self.browser:
            if not self._initialize_browser():
                return False
        
        try:
            # 清除cookies和缓存，确保重新登录
            #self.browser.delete_all_cookies()
            #self.browser.execute_script("window.localStorage.clear();")
            #self.browser.execute_script("window.sessionStorage.clear();")
            
            # 直接打开雪球主页
            #self.browser.get("https://xueqiu.com/")
            
            # 等待页面加载
            WebDriverWait(self.browser, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 检查是否已经登录
            if self._check_login_status():
                self._update_status("已经处于登录状态")
                return True
            
            self._update_status("请在页面中完成登录操作")
            
            # 启动登录状态监控（包含自动刷新功能）
            self._start_login_monitor()
            
            return True
            
        except Exception as e:
            self._update_status(f"登录页面打开失败: {e}")
            return False

    # 修改 _start_login_monitor 方法
    def _start_login_monitor(self):
        """启动登录状态监控 - 添加自动刷新功能"""
        def monitor_login():
            check_count = 0
            max_checks = 60  # 监控60次（约5分钟）
            login_detected = False
            
            while check_count < max_checks and not login_detected:
                try:
                    # 检查当前页面是否有登录成功特征
                    current_url = self.browser.current_url
                    page_html = self.browser.page_source
                    
                    # 登录成功的明确特征
                    if any(x in current_url for x in ['/u/', '/center/', 'xueqiu.com/#/']):
                        login_detected = True
                    elif any(x in page_html for x in ['user__col--name', 'side_user_name', 'xavatar.imedao.com']):
                        login_detected = True
                    elif any(x in self.browser.title for x in ['雪球', '自选', '行情']):
                        login_detected = True
                    
                    if login_detected:
                        self.is_logged_in = True
                        self._update_status("✅ 登录成功检测到！")
                        
                        break
                    
                    time.sleep(5)  # 每5秒检查一次
                    check_count += 1
                    
                    if check_count % 6 == 0:  # 每30秒提示一次
                        self._update_status(f"登录监控中... ({check_count}/60)")
                        
                except Exception as e:
                    self._update_status(f"登录监控错误: {e}")
                    break
            
            if not login_detected:
                self._update_status("❌ 登录监控超时，请检查是否登录成功")
        
        # 在后台线程中运行监控
        monitor_thread = threading.Thread(target=monitor_login, daemon=True)
        monitor_thread.start()

    def _scrape_thread(self):
        """Main scraping thread with independent login handling"""
        # 保存原始窗口句柄（当前标签页）
        original_window = self.browser.current_window_handle if self.browser else None
        
        try:
            # 检查登录状态
            if not self._check_login_status():
                self._update_status("未登录，请先完成登录")
                if not self._manual_login():
                    return
                
                # 等待用户手动登录
                self._update_status("等待登录完成...")
                time.sleep(10)
                
                # 再次检查登录状态
                if not self._check_login_status():
                    self._update_status("登录超时或失败")
                    return
            
            self._update_status(f"开始处理关键字: {self.keyword}")
            
            # 在当前标签页中打开搜索页面（不在新标签页打开）
            search_url = f"https://xueqiu.com/k?q={self.keyword}"
            self._update_status(f"正在访问: {search_url}")
            
            self.browser.get(search_url)
            WebDriverWait(self.browser, 20).until(
                lambda d: d.execute_script('return document.readyState') == 'complete'
            )
            
            # 后续处理逻辑保持不变...
            
        except Exception as e:
            self._update_status(f"爬取过程错误: {e}")
            self.state = ScraperState.ERROR
        finally:
            self.is_running = False
            self.state = ScraperState.COMPLETED

    def start_scraping(self):
        """Start the scraping process"""
        if self.is_running:
            self._update_status("爬虫已经在运行中")
            return
        
        # 确保浏览器已初始化
        if not self.browser:
            if not self._initialize_browser():
                self._update_status("浏览器初始化失败，无法开始爬取")
                return
        
        # 检查登录状态
        if not self._check_login_status():
            self._update_status("请先完成登录操作")
            return
        
        self.is_running = True
        self.state = ScraperState.RUNNING
        self.posts_data.clear()
        
        # 启动爬虫线程
        self.thread = threading.Thread(target=self._scrape_thread, daemon=True)
        self.thread.start()
        self._update_status("爬虫线程已启动")
        
    def stop_scraping(self):
        """Stop scraping"""
        self.is_running = False
        self.state = ScraperState.COMPLETED
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=False)
        
    def _scrape_thread(self):
        """Main scraping thread with proper window handle management"""
        try:
            # 首先检查浏览器是否可用且窗口仍然打开
            if not self.browser:
                self._update_status("浏览器实例不可用，无法开始爬取")
                self.state = ScraperState.ERROR
                return
            
            # 检查浏览器窗口是否仍然打开
            try:
                # 尝试获取当前URL来验证浏览器窗口是否有效
                current_url = self.browser.current_url
                original_window = self.browser.current_window_handle
            except Exception as e:
                self._update_status(f"浏览器窗口已关闭或无效: {e}")
                self.state = ScraperState.ERROR
                return
            
            self._update_status("爬虫线程启动")
            
            # 检查登录状态
            if not self._check_login_status():
                self._update_status("未登录，无法开始爬取")
                self.state = ScraperState.ERROR
                return
            
            # 后续爬取逻辑...
            self._update_status(f"开始处理关键字: {self.keyword}")
            
            search_url = f"https://xueqiu.com/k?q={self.keyword}"
            self._update_status(f"正在访问: {search_url}")
            
            # 在当前标签页打开搜索页面
            try:
                self.browser.get(search_url)
                WebDriverWait(self.browser, 30).until(
                    lambda d: d.execute_script('return document.readyState') == 'complete'
                )
            except Exception as e:
                self._update_status(f"访问搜索页面失败: {e}")
                self.state = ScraperState.ERROR
                return
            
            # 等待内容加载
            try:
                self._wait_for_element(By.CSS_SELECTOR, ".timeline__item, .profiles__timeline__bd", 30)
                time.sleep(3)
                self._update_status("页面加载完成")
            except TimeoutException:
                self._update_status("页面加载超时")
                self.state = ScraperState.ERROR
                return
            
            # 滚动加载内容
            self._scroll_to_load_content()
            
            # 处理项目
            timeline_items = self._find_timeline_items()
            self._update_status(f"找到 {len(timeline_items)} 个项目")
            
            if not timeline_items:
                self._update_status("未找到任何项目")
                self.state = ScraperState.COMPLETED
                return
            
            # 处理项目逻辑...
            items_to_process = timeline_items[:self.max_items] if self.max_items > 0 else timeline_items
            processed_count = 0
            
            for i, item in enumerate(items_to_process):
                if not self.is_running:
                    break
                    
                try:
                    post = self._process_timeline_item(item, i)
                    if post:
                        post.search_keyword = self.keyword
                        self.posts_data.append(post)
                        processed_count += 1
                        self._update_progress(i + 1, len(items_to_process))
                        self._update_status(f"已处理: {post.author_name}")
                        
                except Exception as e:
                    self._update_status(f"处理第 {i+1} 个项目失败: {e}")
                    continue
            
            self._update_status(f"处理完成: {processed_count}/{len(items_to_process)} 个项目成功")
            self._save_results()
            self._update_status("爬取完成")
            
        except Exception as e:
            self._update_status(f"爬取过程错误: {e}")
            self.state = ScraperState.ERROR
        finally:
            self.is_running = False
            if self.state != ScraperState.ERROR:
                self.state = ScraperState.COMPLETED
    
    def _navigate_to_url(self, url: str):
        """Navigate to URL"""
        self.browser.get(url)
        WebDriverWait(self.browser, 20).until(
            lambda d: d.execute_script('return document.readyState') == 'complete'
        )
    
    def _wait_for_element(self, by, value, timeout=None):
        """Wait for element"""
        timeout = timeout or self.config.wait_timeout
        return WebDriverWait(self.browser, timeout).until(
            EC.presence_of_element_located((by, value))
        )
    
    # 修改 _scroll_to_load_content 方法，实现真正的无限滚动
    def _scroll_to_load_content(self):
        """Scroll to load all content with true infinite scrolling"""
        # 确保在当前标签页中操作
        if hasattr(self, 'current_window') and self.browser.current_window_handle != self.current_window:
            self.browser.switch_to.window(self.current_window)
        
        self._update_status("开始无限滚动加载内容...")
        
        last_height = self.browser.execute_script("return document.body.scrollHeight")
        scroll_count = 0
        no_new_content_count = 0
        max_no_new_content = 10  # 增加无新内容的容忍次数
        
        # 获取初始项目数
        items_count_before = len(self._find_timeline_items())
        self._update_status(f"初始找到 {items_count_before} 个项目")
        
        # 无限滚动直到没有新内容
        while no_new_content_count < max_no_new_content and self.is_running:
            
            # 滚动到底部
            self.browser.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            
            # 增加等待时间，确保内容加载
            time.sleep(3.0)  # 增加到3秒等待
            
            # 获取新的高度
            new_height = self.browser.execute_script("return document.body.scrollHeight")
            
            # 检查是否有新内容加载
            current_items = self._find_timeline_items()
            items_count_after = len(current_items)
            
            if new_height == last_height and items_count_after == items_count_before:
                no_new_content_count += 1
                self._update_status(f"没有新内容 ({no_new_content_count}/{max_no_new_content})")
                
                # 尝试其他滚动方式
                if no_new_content_count % 2 == 0:
                    self._try_alternative_scroll_methods()
            else:
                no_new_content_count = 0
                new_items = items_count_after - items_count_before
                self._update_status(f"滚动 {scroll_count + 1}: 加载了 {new_items} 新项目，总项目数: {items_count_after}")
                items_count_before = items_count_after
                last_height = new_height
            
            scroll_count += 1
            
            # 安全限制，防止无限循环
            if scroll_count > 50:  # 最大滚动50次
                self._update_status("达到最大滚动次数限制")
                break
        
        self._update_status(f"滚动完成，共滚动 {scroll_count} 次，找到 {items_count_before} 个项目")
        
        # 最终滚动确保所有内容加载
        self._final_scroll_check()

    # 修改 _process_items 方法来处理最大项目数
    def _process_items(self, items: List[Any], keyword: str) -> List[BlogPost]:
        """处理项目列表，根据最大项目数设置"""
        results = []
        
        # 确定要处理的项目数量
        if self.max_items > 0:
            items_to_process = items[:self.max_items]
            self._update_status(f"将处理前 {self.max_items} 个项目（共 {len(items)} 个）")
        else:
            items_to_process = items  # 处理所有项目
            self._update_status(f"将处理所有 {len(items)} 个项目")
        
        batch_messages = []
        batch_size = 5
        
        for i, item in enumerate(items_to_process):
            if not self.is_running:
                break
                
            try:
                # 确保在当前标签页中操作
                if hasattr(self, 'current_window') and self.browser.current_window_handle != self.current_window:
                    self.browser.switch_to.window(self.current_window)
                
                post = self._process_single_item(item, i, keyword)
                if post:
                    results.append(post)
                    
                    # 批量更新进度
                    if (i + 1) % batch_size == 0 or i + 1 == len(items_to_process):
                        self._update_progress(i + 1, len(items_to_process))
                    
                    batch_messages.append(f"已处理: {post.author_name}")
                    
                    # 批量发送状态消息
                    if len(batch_messages) >= batch_size:
                        self._update_status("\n".join(batch_messages))
                        batch_messages = []
                        
            except Exception as e:
                error_msg = f"处理第 {i+1} 个项目失败: {e}"
                batch_messages.append(error_msg)
                if len(batch_messages) >= batch_size:
                    self._update_status("\n".join(batch_messages))
                    batch_messages = []
                continue
        
        # 发送剩余的消息
        if batch_messages:
            self._update_status("\n".join(batch_messages))
        
        return results

    def _try_alternative_scroll_methods(self):
        """尝试替代的滚动方法"""
        try:
            # 方法1: 滚动到特定元素
            try:
                last_item = self.browser.find_elements(By.CSS_SELECTOR, ".timeline__item")[-1]
                self.browser.execute_script("arguments[0].scrollIntoView(true);", last_item)
                time.sleep(1)
            except:
                pass
            
            # 方法2: 模拟键盘滚动
            self.browser.execute_script("window.scrollBy(0, window.innerHeight / 2);")
            time.sleep(0.5)
            
            # 方法3: 平滑滚动
            self.browser.execute_script("""
                window.scrollTo({
                    top: document.body.scrollHeight,
                    behavior: 'smooth'
                });
            """)
            time.sleep(1.5)
            
        except Exception as e:
            self._update_status(f"替代滚动方法失败: {e}")

    def _final_scroll_check(self):
        """最终滚动检查"""
        try:
            # 滚动到顶部然后到底部，确保所有内容加载
            self.browser.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)
            
            # 缓慢滚动到底部
            self.browser.execute_script("""
                let currentPosition = 0;
                const targetPosition = document.body.scrollHeight;
                const scrollStep = Math.floor(targetPosition / 10);
                
                function smoothScroll() {
                    if (currentPosition < targetPosition) {
                        currentPosition += scrollStep;
                        window.scrollTo(0, currentPosition);
                        setTimeout(smoothScroll, 100);
                    }
                }
                smoothScroll();
            """)
            
            # 等待滚动完成
            time.sleep(3)
            
        except Exception as e:
            self._update_status(f"最终滚动检查失败: {e}")

    def _find_timeline_items(self):
        """更精准地查找timeline项目"""
        try:
            # 使用更精确的选择器
            items = self.browser.find_elements(By.CSS_SELECTOR, "article.timeline__item")
            if not items:
                # 备用选择器
                items = self.browser.find_elements(By.CSS_SELECTOR, ".profiles__timeline__bd > article")
            return items
        except Exception as e:
            self._update_status(f"查找项目失败: {e}")
            return []
    
    # 修改 _process_timeline_item 方法，处理 stale element 错误
    def _process_timeline_item(self, item, index: int) -> Optional[BlogPost]:
        """Process a timeline item with stale element handling"""
        try:
            # 确保在当前标签页中操作
            if hasattr(self, 'current_window') and self.browser.current_window_handle != self.current_window:
                self.browser.switch_to.window(self.current_window)
            
            # 在操作前重新获取元素引用，避免stale element
            try:
                # 通过索引重新查找项目
                timeline_items = self._find_timeline_items()
                if index < len(timeline_items):
                    item = timeline_items[index]
                else:
                    self._update_status(f"项目 {index + 1} 索引超出范围")
                    return None
            except:
                pass
            
            # Scroll to make item visible
            self.browser.execute_script("arguments[0].scrollIntoView({block: 'center'});", item)
            time.sleep(0.5)
            
            # Handle expand buttons
            self._handle_expand_button(item)
            
            # Extract information using strategies
            author_name, profile_url = self._extract_author_info(item)
            user_id = self._extract_user_id(profile_url)
            publish_time, source = self._extract_publish_info(item)
            
            title = self.title_strategy.extract(item, self.browser)
            
            # 在展开后提取内容
            content = self.content_strategy.extract(item, self.browser)
            
            # 如果内容过短，尝试强制展开（使用重新获取的元素）
            if len(content) < 100:
                self._update_status(f"项目 {index + 1} 内容过短，尝试强制方法...")
                
                # 重新获取当前元素引用
                try:
                    current_items = self._find_timeline_items()
                    if index < len(current_items):
                        current_item = current_items[index]
                        content = self._force_expand_content(current_item)
                    else:
                        content = self._force_expand_content(item)
                except:
                    content = self._force_expand_content(item)
            
            likes, comments, reposts, favorites, is_favorited = self.engagement_strategy.extract(item, self.browser)
            has_images, image_count, image_urls = self.image_strategy.extract(item, self.browser)
            
            return BlogPost(
                index=index + 1,
                author_name=author_name,
                user_id=user_id,
                profile_url=profile_url,
                publish_time=publish_time,
                publish_source=source,
                title=title,
                content=content,
                likes=likes,
                comments=comments,
                reposts=reposts,
                favorites=favorites,
                is_favorited=is_favorited,
                has_images=has_images,
                image_count=image_count,
                image_urls=image_urls,
                post_id="未知",
                search_keyword=self.keyword
            )
            
        except Exception as e:
            self._update_status(f"处理项目 {index + 1} 时出错: {e}")
            return None
    
    # 修改 _force_expand_content 方法，确保强制打开详情页
    def _force_expand_content(self, item) -> str:
        """强制展开内容的各种方法，确保打开详情页"""
        original_window = self.browser.current_window_handle
        
        try:
            # 首先尝试在当前页面展开
            try:
                self.browser.execute_script("""
                    const item = arguments[0];
                    // 尝试点击所有可能的展开按钮
                    const buttons = item.querySelectorAll('a, button, [class*="expand"], [class*="more"]');
                    buttons.forEach(btn => {
                        const text = btn.textContent || '';
                        if (text.includes('展开') || text.includes('more') || 
                            text.includes('详情') || btn.href) {
                            btn.click();
                        }
                    });
                """, item)
                time.sleep(3)
            except:
                pass
            
            # 检查是否成功展开
            expanded_content = self.content_strategy.extract(item, self.browser)
            if len(expanded_content) > 100:
                return expanded_content
            
            # 如果当前页面展开失败，强制查找并打开详情链接
            article_link = self._find_article_link_force(item)
            if article_link:
                self._update_status(f"强制打开详情页: {article_link}")
                return self._get_content_from_new_tab_force(article_link, original_window)
            else:
                return "无法找到详情链接"
            
        except Exception as e:
            self._update_status(f"强制展开内容失败: {e}")
            return "内容展开失败"
    
    # 修改 _force_expand_content 方法，避免使用失效的元素
    def _force_expand_content(self, item) -> str:
        """强制展开内容，避免使用stale element"""
        try:
            # 首先尝试在当前页面展开
            try:
                # 使用JavaScript点击，避免直接操作可能失效的元素
                self.browser.execute_script("""
                    const item = arguments[0];
                    // 只点击真正的展开按钮
                    const expandButtons = item.querySelectorAll('[class*="expand"], [class*="more"]');
                    expandButtons.forEach(btn => {
                        const text = btn.textContent || '';
                        if (text.includes('展开') || text.includes('more')) {
                            btn.click();
                        }
                    });
                """, item)
                time.sleep(2)
            except:
                pass
            
            # 重新提取内容
            content = self.content_strategy.extract(item, self.browser)
            if len(content) > 100:
                return content
            
            # 如果当前页面展开失败，检查是否有详情页
            try:
                # 检查是否有详情页结构
                has_longtext = self.browser.execute_script("""
                    return arguments[0].querySelector('.timeline__item__content--longtext') !== null;
                """, item)
                
                if has_longtext:
                    # 查找详情页链接
                    article_link = self.browser.execute_script("""
                        const item = arguments[0];
                        const fakeAnchor = item.querySelector('.timeline__item__content--longtext a.fake-anchor');
                        return fakeAnchor ? fakeAnchor.href : null;
                    """, item)
                    
                    if article_link:
                        original_window = self.browser.current_window_handle
                        return self._get_content_from_new_tab_force(article_link, original_window)
            
            except:
                pass
            
            return content
            
        except Exception as e:
            self._update_status(f"强制展开内容失败: {e}")
            return "内容展开失败"

    # 修改 _find_article_link_force 方法，基于您提供的HTML结构分析
    def _find_article_link_force(self, item) -> Optional[str]:
        """基于HTML结构分析查找文章详情页链接"""
        try:
            # 检查是否有 timeline__item__content--longtext 类（有详情页的标志）
            try:
                longtext_content = item.find_element(By.CSS_SELECTOR, ".timeline__item__content--longtext")
                # 如果有这个类，说明有详情页链接
                self._update_status("检测到详情页内容结构")
            except NoSuchElementException:
                # 没有这个类，说明没有详情页
                self._update_status("无详情页结构，跳过")
                return None
            
            # 在 timeline__item__content--longtext 区域查找链接
            try:
                # 查找 fake-anchor 链接（第一个例子中的隐藏链接）
                fake_anchor = item.find_element(By.CSS_SELECTOR, ".timeline__item__content--longtext a.fake-anchor")
                href = fake_anchor.get_attribute("href")
                if href and "xueqiu.com" in href:
                    self._update_status(f"找到fake-anchor链接: {href}")
                    return href
            except NoSuchElementException:
                pass
            
            # 查找其他可能的链接
            link_selectors = [
                ".timeline__item__content--longtext a[href*='/status/']",
                ".timeline__item__content--longtext a[href*='/article/']",
                ".timeline__item__content--longtext a[href*='/post/']",
                ".timeline__item__content--longtext a[data-id]"
            ]
            
            for selector in link_selectors:
                try:
                    link = item.find_element(By.CSS_SELECTOR, selector)
                    href = link.get_attribute("href")
                    if href and "xueqiu.com" in href:
                        self._update_status(f"找到详情链接: {href}")
                        return href
                except NoSuchElementException:
                    continue
            
            # 如果没有找到链接，尝试从数据属性中提取
            try:
                content_area = item.find_element(By.CSS_SELECTOR, ".timeline__item__content--longtext")
                data_analytics = content_area.get_attribute("data-analytics-data")
                if data_analytics:
                    import json
                    data = json.loads(data_analytics.replace('&quot;', '"'))
                    if 'status_id' in data:
                        user_elem = item.find_element(By.CSS_SELECTOR, ".user-name")
                        user_href = user_elem.get_attribute("href")
                        if user_href:
                            user_id = user_href.split('/')[-1]
                            return f"https://xueqiu.com/{user_id}/{data['status_id']}"
            except:
                pass
            
            self._update_status("未找到详情页链接")
            return None
            
        except Exception as e:
            self._update_status(f"查找文章链接失败: {e}")
            return None
    
    def _get_content_from_new_tab_force(self, article_url: str, original_window: str) -> str:
        """在新标签页中打开详情页面，获取内容后关闭"""
        try:
            # 保存当前窗口句柄
            current_windows = self.browser.window_handles
            
            # 在新标签页中打开链接
            self.browser.execute_script(f"window.open('{article_url}', '_blank');")
            
            # 等待新标签页打开
            WebDriverWait(self.browser, 10).until(
                lambda d: len(d.window_handles) > len(current_windows)
            )
            
            # 获取新窗口句柄
            new_windows = self.browser.window_handles
            new_window = [w for w in new_windows if w not in current_windows][0]
            
            # 切换到新标签页
            self.browser.switch_to.window(new_window)
            
            # 等待页面加载
            WebDriverWait(self.browser, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            time.sleep(2)
            
            # 提取内容
            full_content = self._extract_full_content_from_detail_page()
            
            # 关闭新标签页
            self.browser.close()
            
            # 切换回原始窗口
            self.browser.switch_to.window(original_window)
            
            return full_content
            
        except TimeoutException:
            self._update_status("详情页打开超时")
            try:
                self.browser.switch_to.window(original_window)
            except:
                pass
            return "详情页打开超时"
        except Exception as e:
            self._update_status(f"打开详情页失败: {e}")
            try:
                self.browser.switch_to.window(original_window)
            except:
                pass
            return "打开详情页失败"

    # 确保 _extract_full_content_from_detail_page 方法也在同一个类中
    def _extract_full_content_from_detail_page(self) -> str:
        """从文章详情页提取完整内容"""
        try:
            content_parts = []
            
            # 1. 提取标题
            try:
                title = self.browser.find_element(By.CSS_SELECTOR, ".article__bd__title").text.strip()
                content_parts.append(f"标题: {title}")
            except:
                pass
            
            # 2. 提取作者
            try:
                author = self.browser.find_element(By.CSS_SELECTOR, ".article__author .name").text.strip()
                content_parts.append(f"作者: {author}")
            except:
                pass
            
            # 3. 提取正文
            try:
                detail = self.browser.find_element(By.CSS_SELECTOR, ".article__bd__detail").text.strip()
                if len(detail) > 100:
                    content_parts.append(detail)
            except:
                # 备用：获取整个页面文本
                body = self.browser.find_element(By.TAG_NAME, "body").text
                content_parts.append(body)
            
            return "\n\n".join(content_parts) if content_parts else "无法提取内容"
            
        except Exception as e:
            return f"内容提取错误: {e}"

    # 修改 _handle_expand_button 方法，确保所有项目都能展开
    def _handle_expand_button(self, item):
        """Handle expand button for all timeline items with improved logic"""
        try:
            # 尝试多种展开按钮选择器
            expand_selectors = [
                ".timeline__expand__control",
                ".expand-control", 
                ".show-more",
                "[class*='expand']",
                "[class*='more']",
                "a:contains('展开')",
                "button:contains('展开')",
                "a:contains('more')",
                "button:contains('more')"
            ]
            
            max_attempts = 3  # 最大尝试次数
            attempts = 0
            
            while attempts < max_attempts:
                found_expand = False
                
                for selector in expand_selectors:
                    try:
                        # 查找当前可见的展开按钮
                        expand_btns = item.find_elements(By.CSS_SELECTOR, selector)
                        for btn in expand_btns:
                            try:
                                # 检查按钮是否可见且包含展开相关文本
                                if (btn.is_displayed() and 
                                    ("展开" in btn.text or "more" in btn.text.lower() or 
                                    "expand" in btn.text.lower() or "显示更多" in btn.text)):
                                    
                                    # 使用JavaScript点击，更可靠
                                    self.browser.execute_script("arguments[0].click();", btn)
                                    self._update_status(f"点击展开按钮: {btn.text}")
                                    
                                    # 等待内容展开
                                    time.sleep(2.0)  # 增加等待时间
                                    found_expand = True
                                    break
                                    
                            except Exception as e:
                                continue
                        
                        if found_expand:
                            break
                            
                    except NoSuchElementException:
                        continue
                
                # 检查内容是否已经展开
                if self._is_content_expanded(item):
                    break
                    
                attempts += 1
                if attempts < max_attempts:
                    time.sleep(1)  # 等待后重试
                    
        except Exception as e:
            # 静默处理错误，不影响主流程
            pass
    
    # 添加检查内容是否已展开的方法
    def _is_content_expanded(self, item) -> bool:
        """检查内容是否已经展开"""
        try:
            # 检查展开的内容区域
            expanded_selectors = [
                ".timeline__item__content--expanded",
                ".content--expanded", 
                ".content--full",
                "[class*='expanded']",
                "[class*='unfold']"
            ]
            
            for selector in expanded_selectors:
                try:
                    expanded_elems = item.find_elements(By.CSS_SELECTOR, selector)
                    for elem in expanded_elems:
                        if elem.is_displayed():
                            return True
                except NoSuchElementException:
                    continue
            
            # 检查收起按钮（展开后会出现）
            try:
                collapse_btns = item.find_elements(By.CSS_SELECTOR, ".timeline__unfold__control")
                for btn in collapse_btns:
                    if btn.is_displayed() and "收起" in btn.text:
                        return True
            except NoSuchElementException:
                pass
                
            return False
            
        except Exception as e:
            return False
    
    def _extract_author_info(self, item):
        """Extract author information"""
        try:
            name_elem = item.find_element(By.CSS_SELECTOR, ".user-name")
            name = name_elem.text.strip()
            profile_url = name_elem.get_attribute("href") or "无链接"
            return name, profile_url
        except:
            return "未知", "无链接"
    
    def _extract_user_id(self, profile_url: str) -> str:
        """Extract user ID from profile URL"""
        if profile_url != "无链接" and "/" in profile_url:
            parts = profile_url.split("/")
            return parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else "未知"
        return "未知"
    
    def _extract_publish_info(self, item):
        """Extract publish time and source"""
        try:
            time_elem = item.find_element(By.CSS_SELECTOR, "a.date-and-source")
            full_text = time_elem.text.strip()
            
            if "·" in full_text:
                time_part, source_part = full_text.split("·", 1)
                return time_part.strip(), source_part.strip()
            else:
                return full_text, "未知来源"
        except:
            return "未知时间", "未知来源"
    
    def _update_status(self, message: str):
        """Update status through message queue"""
        if self.message_queue:
            self.message_queue.put_message('status', {
                'tab_id': self.tab_id,
                'message': message
            })
    
    def _update_progress(self, current: int, total: int):
        """Update progress through message queue"""
        if self.message_queue:
            self.message_queue.put_message('progress', {
                'tab_id': self.tab_id,
                'current': current,
                'total': total
            })
    
    def _save_results(self):
        """Save results to JSON file"""
        if not self.posts_data:
            self._update_status("没有数据可保存")
            return
            
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"xueqiu_tab_{self.tab_id}_{timestamp}.json"
        
        try:
            data_to_save = [post.to_dict() for post in self.posts_data]
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
                
            self._update_status(f"结果已保存到: {filename}")
        except Exception as e:
            self._update_status(f"保存结果失败: {e}")

# ==================== MAIN GUI APPLICATION ====================

class XueqiuGUI(tk.Tk):
    """Main GUI application"""
    
    # 修改 __init__ 方法中的启动调用
    def __init__(self):
        super().__init__()
        self.title("雪球多标签爬虫工具")
        self.geometry("800x600")
        self.configure(bg='#f0f0f0')
        
        self.config_manager = ConfigManager()
        self.config = self.config_manager.get_scraper_config()
        self.browser = None
        self.is_logged_in = False
        self.tabs = {}
        self.current_tab_id = 0

        # 改为每个标签页独立的消息处理
        self.tab_message_queues = {}
        
        self.setup_gui()
        #self.setup_browser()
        
        # 启动消息处理定时器
        self._start_message_processor()
        
        # 启动登录状态检查线程（在后台运行）
        #self._start_login_status_check()

    
        
    def stop_scraping(self):
        """停止爬虫并关闭浏览器"""
        self.is_running = False
        self.state = ScraperState.COMPLETED
        if self.browser:
            try:
                self.browser.quit()
            except Exception:
                pass
            finally:
                self.browser = None

    def _start_message_processor(self):
        """启动每个标签页的消息处理器"""
        def process_messages():
            try:
                # 处理所有标签页的消息
                for tab_id, tab_data in self.tabs.items():
                    if 'scraper' in tab_data and tab_data['scraper']:
                        has_more = tab_data['scraper'].message_queue.process_messages()
                        # 如果还有消息，快速再次处理
                        if has_more:
                            self.after(10, process_messages)
                            return
                
                # 没有消息时，降低检查频率
                self.after(100, process_messages)
                
            except Exception as e:
                logger.error(f"Message processor error: {e}")
                self.after(100, process_messages)
        
        process_messages()
    
    def _start_login_status_check(self):
        """启动登录状态检查（在单独线程中运行）"""
        def check_status_thread():
            """在单独线程中运行的登录状态检查"""
            while True:
                try:
                    if hasattr(self, 'browser') and self.browser:
                        # 在GUI线程中更新状态
                        self.after(0, self._update_login_status_gui)
                    
                    # 每10秒检查一次
                    time.sleep(10)
                    
                except Exception as e:
                    logger.error(f"Login status check thread error: {e}")
                    time.sleep(10)  # 出错后等待10秒再继续
        
        # 启动登录状态检查线程
        login_thread = threading.Thread(target=check_status_thread, daemon=True)
        login_thread.start()

    # 添加 _update_login_status_gui 方法
    def _update_login_status_gui(self):
        """在GUI线程中更新登录状态"""
        try:
            is_logged_in = self._check_login_status_internal()
            
            if is_logged_in:
                self.login_status.config(text="已登录", foreground="green")
                self.is_logged_in = True
            else:
                self.login_status.config(text="未登录", foreground="red")
                self.is_logged_in = False
                
        except Exception as e:
            logger.error(f"GUI login status update error: {e}")
            self.login_status.config(text="状态检查失败", foreground="orange")

    
    
    def handle_tab_message(self, tab_id: int, message_type: str, data: Any):
        """处理单个标签页的消息"""
        if tab_id not in self.tabs:
            return
        
        tab_data = self.tabs[tab_id]
        
        try:
            if message_type == 'status':
                message = data['message']
                results_text = tab_data['results_text']
                results_text.config(state=tk.NORMAL)
                results_text.insert(tk.END, f"{message}\n")
                
                # 限制文本长度
                current_line_str = results_text.index(tk.END).split('.')[0]
                try:
                    current_line_count = int(current_line_str)
                    if current_line_count > 1000:
                        results_text.delete(1.0, "100.0")
                except ValueError:
                    pass
                
                results_text.see(tk.END)
                results_text.config(state=tk.DISABLED)
            
            elif message_type == 'progress':
                current = data['current']
                total = data['total']
                tab_data['progress_var'].set(f"{current}/{total}")
                
        except Exception as e:
            logger.error(f"Error handling tab message: {e}")
    
    def setup_gui(self):
        # 设置主题样式
        style = ttk.Style()
        style.configure('TFrame', background='#f5f5f5')
        style.configure('TLabel', background='#f5f5f5', font=('Microsoft YaHei', 9))
        style.configure('TButton', font=('Microsoft YaHei', 9))
        style.configure('TLabelFrame', background='#f5f5f5', font=('Microsoft YaHei', 10, 'bold'))
        style.configure('TNotebook', background='#f5f5f5')
        style.configure('TNotebook.Tab', font=('Microsoft YaHei', 9, 'bold'))
        
        # 主框架
        main_frame = ttk.Frame(self, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="雪球多标签数据爬取工具", 
                            font=("Microsoft YaHei", 10, "bold"), foreground="#2c3e50")
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # 控制面板 - 简化布局
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 只保留添加标签按钮
        add_tab_btn = ttk.Button(control_frame, text="+ 添加标签", command=self.add_new_tab, width=12)
        add_tab_btn.pack(side=tk.RIGHT)
        
        # 标签控制
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, 
                            font=('Microsoft YaHei', 8), foreground='#666666')
        status_bar.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E))
        
        # 配置权重
        self.columnconfigure(0, weight=1)
        self.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 添加初始标签
        self.add_new_tab()
    
       
    def manual_login(self):
        """手动登录现在由各个标签页自己处理，并显示状态"""
        current_tab = self.notebook.select()
        if current_tab:
            tab_index = self.notebook.index(current_tab)
            tab_id = list(self.tabs.keys())[tab_index]
            tab_data = self.tabs[tab_id]
            
            if not tab_data['scraper']:
                # 如果没有爬虫实例，先创建一个
                tab_data['scraper'] = XueqiuScraperTab(
                    tab_id, 
                    tab_data['keyword_var'].get(), 
                    tab_data['max_items_var'].get(),
                    self.message_queue, 
                    self.config
                )
            
            # 更新状态显示
            results_text = tab_data['results_text']
            results_text.config(state=tk.NORMAL)
            results_text.insert(tk.END, "开始登录流程...\n")
            results_text.config(state=tk.DISABLED)
            
            # 执行登录
            tab_data['scraper']._manual_login()
        else:
            messagebox.showinfo("提示", "请先选择一个标签页")

    def check_login_status(self):
        """登录状态检查现在由各个标签页自己处理"""
        # 这个方法可以移除或修改为调用各个标签页的检查方法
        pass

    def add_new_tab(self):
        """Add a new search tab with individual login controls"""
        self.current_tab_id += 1
        tab_id = self.current_tab_id
        
        # 创建标签框架
        tab_frame = ttk.Frame(self.notebook, padding="8")
        
        # 搜索设置区域
        settings_frame = ttk.LabelFrame(tab_frame, text="搜索设置", padding="6")
        settings_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 8))
        
        # 第一行：关键字和登录状态
        row1_frame = ttk.Frame(settings_frame)
        row1_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Label(row1_frame, text="关键字:").pack(side=tk.LEFT, padx=(0, 5))
        keyword_var = tk.StringVar(value="东方财富")
        keyword_entry = ttk.Entry(row1_frame, textvariable=keyword_var, width=20)
        keyword_entry.pack(side=tk.LEFT, padx=(0, 15))
        
        # 标签页单独的登录状态
        login_status_frame = ttk.Frame(row1_frame)
        login_status_frame.pack(side=tk.LEFT)
        
        ttk.Label(login_status_frame, text="状态:").pack(side=tk.LEFT)
        login_status_var = tk.StringVar(value="未初始化")
        login_status_label = ttk.Label(login_status_frame, textvariable=login_status_var, 
                                    foreground="orange", font=('Microsoft YaHei', 9))
        login_status_label.pack(side=tk.LEFT, padx=(5, 5))
        
        login_btn = ttk.Button(login_status_frame, text="登录", width=6,
                            command=lambda: self.login_tab(tab_id))
        login_btn.pack(side=tk.LEFT)
        
        # 第二行：数量设置
        row2_frame = ttk.Frame(settings_frame)
        row2_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        ttk.Label(row2_frame, text="最大数量:").pack(side=tk.LEFT, padx=(0, 5))
        max_items_var = tk.IntVar(value=0)
        max_items_spin = ttk.Spinbox(row2_frame, from_=1, to=100, textvariable=max_items_var, width=6)
        max_items_spin.pack(side=tk.LEFT)
        
        settings_frame.columnconfigure(0, weight=1)
        
        # 按钮区域
        button_frame = ttk.Frame(tab_frame)
        button_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 8))
        
        start_btn = ttk.Button(button_frame, text="开始", width=8,
                            command=lambda: self.start_tab_scraping(tab_id, keyword_var.get(), max_items_var.get()))
        start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        stop_btn = ttk.Button(button_frame, text="停止", width=6,
                            command=lambda: self.stop_tab_scraping(tab_id))
        stop_btn.pack(side=tk.LEFT)
        
        # 进度显示
        progress_var = tk.StringVar(value="0/0")
        progress_label = ttk.Label(button_frame, textvariable=progress_var, foreground="#7f8c8d")
        progress_label.pack(side=tk.RIGHT)
        
        # 结果区域
        results_frame = ttk.LabelFrame(tab_frame, text="日志输出", padding="6")
        results_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        results_text = scrolledtext.ScrolledText(results_frame, height=15, width=80, 
                                            font=('Consolas', 9), wrap=tk.WORD)
        results_text.pack(fill=tk.BOTH, expand=True)
        results_text.config(state=tk.DISABLED)
        
        # 配置权重
        tab_frame.columnconfigure(0, weight=1)
        tab_frame.rowconfigure(2, weight=1)
        
        # 添加到笔记本
        tab_name = f"标签 {tab_id}"
        self.notebook.add(tab_frame, text=tab_name)
        
        # 存储标签数据
        self.tabs[tab_id] = {
            'frame': tab_frame,
            'keyword_var': keyword_var,
            'max_items_var': max_items_var,
            'login_status_var': login_status_var,
            'progress_var': progress_var,
            'results_text': results_text,
            'scraper': None
        }
        
        self.notebook.select(len(self.notebook.tabs()) - 1)

    def _check_tab_login_status(self, tab_id):
        """检查标签页登录状态"""
        tab_data = self.tabs.get(tab_id)
        if tab_data and tab_data['scraper']:
            try:
                if tab_data['scraper'].browser:
                    # 检查登录状态并更新UI
                    is_logged_in = tab_data['scraper']._check_login_status()
                    if is_logged_in:
                        tab_data['login_status_var'].set("已登录")
                    else:
                        tab_data['login_status_var'].set("未登录")
                else:
                    tab_data['login_status_var'].set("浏览器未就绪")
            except Exception as e:
                tab_data['login_status_var'].set("检查失败")

    def login_tab(self, tab_id):
        """手动登录指定标签页"""
        tab_data = self.tabs[tab_id]
        
        if not tab_data['scraper']:
            # 如果没有爬虫实例，先创建一个
            tab_data['scraper'] = XueqiuScraperTab(
                tab_id, 
                tab_data['keyword_var'].get(), 
                tab_data['max_items_var'].get(),
                self.handle_tab_message,  # 传入正确的回调函数
                self.config
            )
        
        # 更新状态显示
        results_text = tab_data['results_text']
        results_text.config(state=tk.NORMAL)
        results_text.insert(tk.END, "开始登录流程...\n")
        results_text.config(state=tk.DISABLED)
        
        # 执行登录
        tab_data['scraper'].manual_login()

    def _start_tab_login_check(self, tab_id):
        """启动标签页登录状态检查"""
        def check_tab_login_status():
            tab_data = self.tabs.get(tab_id)
            if tab_data and tab_data['scraper']:
                is_logged_in = tab_data['scraper']._check_login_status()
                if is_logged_in:
                    tab_data['login_status_var'].set("已登录")
                else:
                    tab_data['login_status_var'].set("未登录")
        
        # 延迟检查登录状态
        self.after(5000, check_tab_login_status)

    # 修改 XueqiuGUI 类中的 start_tab_scraping 方法
    def start_tab_scraping(self, tab_id, keyword, max_items):
        """Start scraping for a specific tab with independent browser instance"""
        tab_data = self.tabs[tab_id]
        
         # 创建爬虫实例，传入GUI回调函数
        scraper = XueqiuScraperTab(
            tab_id, keyword, max_items, 
            self.handle_tab_message,  # 传入消息处理回调
            self.config
        )
        tab_data['scraper'] = scraper
        
        # Update UI
        tab_data['progress_var'].set("0/0")
        results_text = tab_data['results_text']
        results_text.config(state=tk.NORMAL)
        results_text.delete(1.0, tk.END)
        results_text.insert(tk.END, f"开始爬取关键字: {keyword}\n")
        results_text.config(state=tk.DISABLED)
        
        # 更新主状态栏
        self.status_var.set(f"标签 {tab_id}: 开始爬取 {keyword}")
        
        # Start scraping
        scraper.start_scraping()
    
    def stop_tab_scraping(self, tab_id):
        """Stop scraping for a specific tab"""
        tab_data = self.tabs[tab_id]
        if tab_data['scraper']:
            tab_data['scraper'].stop_scraping()
            tab_data['status_label'].config(text="已停止")
    
    # 修改 on_closing 方法，确保浏览器正确关闭
    # 修改 on_closing 方法
    def on_closing(self):
        """Handle application closing - 现在每个标签页自己管理浏览器"""
        # 停止所有爬虫（每个爬虫会自己关闭浏览器）
        for tab_data in self.tabs.values():
            if tab_data['scraper']:
                tab_data['scraper'].stop_scraping()
        
        # 不再需要关闭主浏览器实例
        self.destroy()
    
    # 修改 _force_close_browser 方法
    def _force_close_browser(self):
        """强制关闭浏览器进程"""
        try:
            import os
            if os.name == 'nt':  # Windows
                os.system('taskkill /f /im chromedriver.exe /t 2>nul')
                os.system('taskkill /f /im chrome.exe /t 2>nul')
            else:  # Linux/Mac
                os.system('pkill -f chromedriver 2>/dev/null')
                os.system('pkill -f chrome 2>/dev/null')
        except:
            pass

# 修改 main 函数，添加更好的异常处理
def main():
    """Main function with improved error handling"""
    try:
        app = XueqiuGUI()
        app.protocol("WM_DELETE_WINDOW", app.on_closing)
        app.mainloop()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        # 尝试优雅关闭
        try:
            if 'app' in locals():
                app.on_closing()
        except:
            pass
    except Exception as e:
        messagebox.showerror("启动错误", f"程序启动失败: {e}")
        # 记录详细错误信息
        import traceback
        with open('error.log', 'w', encoding='utf-8') as f:
            f.write(traceback.format_exc())

if __name__ == "__main__":
    main()