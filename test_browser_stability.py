"""
测试浏览器稳定性和登录功能
"""

import time
import threading
from gui_scraper import XueqiuScraperTab, ScraperConfig

def test_browser_persistence():
    """测试浏览器持久性 - 确保不会自动关闭"""
    print("🌐 测试浏览器持久性...")
    
    config = ScraperConfig()
    config.headless = False  # 使用有界面模式
    
    def callback(tab_id, msg_type, data):
        if msg_type == 'status':
            print(f"[Tab {tab_id}] {data.get('message', '')}")
    
    scraper = XueqiuScraperTab(
        tab_id=1,
        keyword="测试",
        max_items=5,
        gui_callback=callback,
        config=config
    )
    
    try:
        # 初始化浏览器
        print("正在初始化浏览器...")
        if scraper._initialize_browser():
            print("✅ 浏览器初始化成功")
            
            # 等待一段时间，检查浏览器是否保持打开
            print("等待30秒，检查浏览器是否保持打开...")
            for i in range(30):
                time.sleep(1)
                if scraper._is_browser_alive():
                    if i % 5 == 0:
                        print(f"   浏览器状态正常 ({i+1}/30秒)")
                else:
                    print(f"❌ 浏览器在 {i+1} 秒后关闭")
                    return False
            
            print("✅ 浏览器保持打开30秒，测试通过")
            
            # 测试登录流程
            print("\n🔑 测试登录流程...")
            if scraper.manual_login():
                print("✅ 登录流程启动成功")
                print("💡 请在浏览器中完成登录，然后等待自动检测...")
                
                # 等待登录检测
                for i in range(60):  # 等待60秒
                    time.sleep(1)
                    if scraper.is_logged_in:
                        print("✅ 登录成功检测到！")
                        break
                    if i % 10 == 9:
                        print(f"   等待登录中... ({i+1}/60秒)")
                
                if not scraper.is_logged_in:
                    print("⏰ 登录检测超时，但这是正常的（需要手动登录）")
            else:
                print("❌ 登录流程启动失败")
                return False
            
            # 清理
            scraper._cleanup_browser()
            print("✅ 浏览器清理完成")
            return True
            
        else:
            print("❌ 浏览器初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        try:
            scraper._cleanup_browser()
        except:
            pass
        return False

def test_browser_recovery():
    """测试浏览器恢复功能"""
    print("\n🔄 测试浏览器恢复功能...")
    
    config = ScraperConfig()
    config.headless = False
    
    def callback(tab_id, msg_type, data):
        if msg_type == 'status':
            print(f"[Tab {tab_id}] {data.get('message', '')}")
    
    scraper = XueqiuScraperTab(
        tab_id=2,
        keyword="测试恢复",
        max_items=5,
        gui_callback=callback,
        config=config
    )
    
    try:
        # 初始化浏览器
        if scraper._initialize_browser():
            print("✅ 浏览器初始化成功")
            
            # 模拟浏览器关闭
            print("模拟浏览器关闭...")
            scraper.browser.quit()
            
            # 测试检测功能
            if not scraper._is_browser_alive():
                print("✅ 正确检测到浏览器关闭")
            else:
                print("❌ 未能检测到浏览器关闭")
                return False
            
            # 测试恢复功能
            print("测试浏览器恢复...")
            if scraper._ensure_browser_alive():
                print("✅ 浏览器恢复成功")
                
                # 验证恢复后的浏览器可用
                if scraper._is_browser_alive():
                    print("✅ 恢复后的浏览器工作正常")
                    scraper._cleanup_browser()
                    return True
                else:
                    print("❌ 恢复后的浏览器无法工作")
                    return False
            else:
                print("❌ 浏览器恢复失败")
                return False
                
        else:
            print("❌ 浏览器初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 恢复测试过程中出现错误: {e}")
        try:
            scraper._cleanup_browser()
        except:
            pass
        return False

def main():
    """运行所有测试"""
    print("🚀 开始浏览器稳定性测试...\n")
    
    tests = [
        ("浏览器持久性", test_browser_persistence),
        ("浏览器恢复功能", test_browser_recovery),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有浏览器稳定性测试通过！")
        print("💡 浏览器关闭问题已修复")
    else:
        print("⚠️ 部分测试失败，可能仍存在问题")

if __name__ == "__main__":
    main()
