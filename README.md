# 雪球GUI爬虫 (Xueqiu GUI Scraper)

一个功能强大的雪球网站内容爬取工具，具有图形用户界面，支持多标签页并发爬取。

## 🚀 功能特性

- **图形用户界面**: 基于tkinter的直观GUI界面
- **多标签页支持**: 同时处理多个搜索关键词
- **智能登录检测**: 自动检测登录状态，支持手动登录
- **内容提取**: 提取博文标题、内容、作者信息、互动数据等
- **数据导出**: 支持JSON格式数据导出
- **浏览器管理**: 智能浏览器实例管理和错误恢复
- **实时状态更新**: 实时显示爬取进度和状态

## 📋 系统要求

- Python 3.7+
- Windows 10/11 (主要测试环境)
- Chrome浏览器
- ChromeDriver (自动下载)

## 🛠️ 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd xueqiu_gui_scraper
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements_gui.txt
   ```

3. **运行程序**
   ```bash
   python gui_scraper.py
   ```
   或者双击运行：
   ```bash
   run_gui.bat
   ```

## 🎯 使用方法

### 基本使用流程

1. **启动程序**: 运行 `gui_scraper.py` 或 `run_gui.bat`
2. **添加标签页**: 点击"添加标签页"按钮
3. **设置搜索参数**:
   - 输入搜索关键词
   - 设置最大爬取项目数 (0表示不限制)
4. **登录雪球**: 点击"登录"按钮，在弹出的浏览器中完成登录
5. **开始爬取**: 点击"开始爬取"按钮
6. **查看结果**: 在结果区域查看爬取的数据
7. **导出数据**: 点击"导出JSON"保存数据

### 界面说明

- **标签页管理**: 支持多个搜索任务同时进行
- **状态显示**: 实时显示登录状态和爬取进度
- **结果预览**: 表格形式显示爬取结果
- **日志输出**: 详细的操作日志和错误信息

## ⚙️ 配置选项

程序会自动创建 `config.json` 配置文件，包含以下选项：

```json
{
  "chrome_driver_path": "自动检测ChromeDriver路径",
  "max_scrolls": 20,
  "scroll_interval": 1.5,
  "max_no_new_content": 3,
  "wait_timeout": 20,
  "headless": false,
  "user_data_dir": "chrome_profile",
  "browser_type": "CHROME",
  "max_concurrent_tabs": 5
}
```

## 📊 数据结构

爬取的数据包含以下字段：

- `index`: 项目序号
- `author_name`: 作者名称
- `user_id`: 用户ID
- `profile_url`: 用户主页URL
- `publish_time`: 发布时间
- `title`: 博文标题
- `content`: 博文内容
- `likes`: 点赞数
- `comments`: 评论数
- `reposts`: 转发数
- `favorites`: 收藏数
- `has_images`: 是否包含图片
- `image_count`: 图片数量
- `image_urls`: 图片URL列表
- `search_keyword`: 搜索关键词

## 🔧 故障排除

### 常见问题

1. **浏览器初始化失败**
   - 检查ChromeDriver是否正确安装
   - 确保Chrome浏览器已安装
   - 尝试重新启动程序

2. **登录检测失败**
   - 手动刷新浏览器页面
   - 确保已完成登录流程
   - 检查网络连接

3. **爬取数据为空**
   - 检查搜索关键词是否正确
   - 确认已登录雪球账户
   - 尝试减少并发标签页数量

### 日志文件

程序会生成以下日志文件：
- `xueqiu_gui_scraper.log`: 主程序日志
- `error.log`: 错误日志

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。

## ⚠️ 免责声明

- 本工具仅用于学习和研究目的
- 使用时请遵守目标网站的robots.txt和使用条款
- 请合理控制爬取频率，避免对服务器造成过大负担
- 用户需自行承担使用本工具的风险和责任
