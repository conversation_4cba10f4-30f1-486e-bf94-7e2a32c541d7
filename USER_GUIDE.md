# 用户使用指南

## 🎯 快速开始

### 第一次使用

1. **启动程序**
   - 双击 `run_gui.bat` 或运行 `python gui_scraper.py`
   - 程序会自动检测和下载ChromeDriver（如果需要）

2. **界面介绍**
   - 主窗口包含标签页管理区域
   - 每个标签页代表一个独立的爬取任务
   - 底部显示全局状态信息

## 📝 详细操作步骤

### 1. 添加爬取任务

1. 点击 **"添加标签页"** 按钮
2. 在新标签页中设置：
   - **搜索关键词**: 要搜索的内容（如"股票"、"基金"等）
   - **最大项目数**: 要爬取的最大条目数（0表示不限制）

### 2. 登录雪球账户

1. 点击 **"登录"** 按钮
2. 程序会打开Chrome浏览器并导航到雪球网站
3. 在浏览器中完成登录操作：
   - 输入手机号/用户名
   - 输入密码或验证码
   - 完成登录流程
4. 登录成功后，状态会显示为"已登录"

### 3. 开始爬取

1. 确认已登录状态
2. 点击 **"开始爬取"** 按钮
3. 程序会：
   - 自动访问搜索页面
   - 滚动加载更多内容
   - 提取博文信息
   - 实时更新进度

### 4. 查看结果

- **实时预览**: 在表格中查看已爬取的数据
- **状态监控**: 查看当前爬取状态和进度
- **日志信息**: 在日志区域查看详细操作记录

### 5. 导出数据

1. 爬取完成后，点击 **"导出JSON"** 按钮
2. 选择保存位置和文件名
3. 数据将以JSON格式保存

## 🔧 高级功能

### 多任务并行

1. 可以同时添加多个标签页
2. 每个标签页可以设置不同的搜索关键词
3. 各标签页独立运行，互不影响

### 浏览器管理

- **自动恢复**: 如果浏览器连接断开，程序会自动尝试重连
- **独立实例**: 每个标签页使用独立的浏览器实例
- **智能检测**: 自动检测登录状态变化

### 数据过滤

程序会自动过滤和清理：
- 重复内容
- 无效数据
- 广告内容

## ⚙️ 配置选项

### 基本配置

可以通过修改 `config.json` 文件调整以下参数：

```json
{
  "max_scrolls": 20,           // 最大滚动次数
  "scroll_interval": 1.5,      // 滚动间隔（秒）
  "max_no_new_content": 3,     // 无新内容最大容忍次数
  "wait_timeout": 20,          // 等待超时时间（秒）
  "headless": false,           // 是否使用无头模式
  "max_concurrent_tabs": 5     // 最大并发标签页数
}
```

### 高级配置

- **ChromeDriver路径**: 自动检测，也可手动指定
- **用户数据目录**: 浏览器配置文件存储位置
- **浏览器类型**: 目前支持Chrome

## 🚨 注意事项

### 使用限制

1. **频率控制**: 避免过于频繁的请求
2. **数据量限制**: 建议单次爬取不超过1000条
3. **网络稳定**: 确保网络连接稳定

### 最佳实践

1. **分批处理**: 大量数据建议分批爬取
2. **定期清理**: 定期清理浏览器缓存和日志文件
3. **备份数据**: 及时导出和备份重要数据

### 错误处理

- **登录失败**: 检查账户状态，重新登录
- **爬取中断**: 程序会自动尝试恢复，也可手动重启
- **数据异常**: 检查网络连接和目标网站状态

## 📊 数据说明

### 导出格式

数据以JSON格式导出，包含以下字段：

- **基本信息**: 作者、标题、内容、发布时间
- **互动数据**: 点赞、评论、转发、收藏数
- **媒体信息**: 图片数量和URL
- **元数据**: 搜索关键词、爬取时间等

### 数据质量

- **完整性**: 尽可能提取所有可用信息
- **准确性**: 多重验证确保数据准确
- **一致性**: 统一的数据格式和结构

## 🔍 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python版本（需要3.7+）
   - 确认依赖包已安装
   - 查看错误日志

2. **浏览器打开失败**
   - 检查Chrome浏览器是否已安装
   - 确认ChromeDriver版本匹配
   - 尝试重新下载ChromeDriver

3. **登录检测失败**
   - 手动刷新浏览器页面
   - 确认登录流程已完成
   - 检查网络连接状态

4. **爬取数据为空**
   - 确认搜索关键词正确
   - 检查登录状态
   - 验证网站可访问性

### 日志分析

- **主日志**: `xueqiu_gui_scraper.log`
- **错误日志**: `error.log`
- **浏览器日志**: 控制台输出

## 📞 技术支持

如遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查网络连接和目标网站状态
3. 尝试重启程序和浏览器
4. 参考故障排除指南

## 🔄 版本更新

程序会不断改进和更新，建议：

1. 定期检查更新
2. 备份重要配置和数据
3. 关注功能变更说明
