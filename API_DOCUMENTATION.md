# API 文档

## 核心类和方法

### ScraperConfig
爬虫配置类，包含所有配置参数。

```python
@dataclass
class ScraperConfig:
    chrome_driver_path: str = r'd:/myCursor/XDB3/chromedriver/chromedriver.exe'
    max_scrolls: int = 20
    scroll_interval: float = 1.5
    max_no_new_content: int = 3
    wait_timeout: int = 20
    headless: bool = False
    user_data_dir: str = "chrome_profile"
    browser_type: BrowserType = BrowserType.CHROME
    max_concurrent_tabs: int = 5
```

### XueqiuScraperTab
单个标签页的爬虫实例。

#### 初始化
```python
def __init__(self, tab_id: int, keyword: str, max_items: int, gui_callback: Callable, config: ScraperConfig)
```

#### 主要方法

##### start_scraping()
开始爬取过程
```python
def start_scraping(self):
    """Start the scraping process"""
```

##### stop_scraping()
停止爬取过程
```python
def stop_scraping(self):
    """Stop scraping"""
```

##### manual_login()
手动登录
```python
def manual_login(self):
    """手动登录的公共方法"""
```

### 内容提取策略

#### TitleExtractionStrategy
标题提取策略
```python
def extract(self, item, driver=None) -> str:
    """提取博文标题"""
```

#### ContentExtractionStrategyImpl
内容提取策略
```python
def extract(self, item, driver=None) -> str:
    """提取博文内容"""
```

#### ImageExtractionStrategy
图片信息提取策略
```python
def extract(self, item, driver=None) -> tuple:
    """提取图片信息，返回(has_images, image_count, image_urls)"""
```

#### EngagementExtractionStrategy
互动数据提取策略
```python
def extract(self, item, driver=None) -> tuple:
    """提取互动数据，返回(likes, comments, reposts, favorites, is_favorited)"""
```

### BrowserFactory
浏览器工厂类

#### create_browser()
创建浏览器实例
```python
@staticmethod
def create_browser(config: ScraperConfig) -> Optional[webdriver.Remote]:
    """Create a browser instance based on configuration"""
```

### ConfigManager
配置管理器

#### 初始化
```python
def __init__(self, config_file: str = "config.json"):
    """初始化配置管理器"""
```

#### get_scraper_config()
获取爬虫配置
```python
def get_scraper_config(self) -> ScraperConfig:
    """Convert to ScraperConfig object"""
```

#### save_config()
保存配置
```python
def save_config(self):
    """Save configuration to file"""
```

### XueqiuScraperGUI
主GUI类

#### 初始化
```python
def __init__(self):
    """初始化GUI界面"""
```

#### 主要方法

##### add_tab()
添加新标签页
```python
def add_tab(self):
    """添加新的爬虫标签页"""
```

##### start_scraping_tab()
开始指定标签页的爬取
```python
def start_scraping_tab(self, tab_id):
    """开始指定标签页的爬取"""
```

##### export_json_tab()
导出指定标签页的数据
```python
def export_json_tab(self, tab_id):
    """导出指定标签页的数据为JSON"""
```

## 数据结构

### BlogPost
博文数据结构
```python
@dataclass
class BlogPost:
    index: int
    author_name: str
    user_id: str
    profile_url: str
    publish_time: str
    title: str
    content: str
    likes: int
    comments: int
    reposts: int
    has_images: bool
    favorites: int = 0
    is_favorited: bool = False
    image_count: int = 0
    search_keyword: str = ""
    publish_source: str = "未知来源"
    image_urls: List[str] = None
    post_id: str = "未知"
```

### ScraperState
爬虫状态枚举
```python
class ScraperState(Enum):
    INITIALIZED = auto()
    LOGIN_REQUIRED = auto()
    LOGGED_IN = auto()
    SCRAPING = auto()
    COMPLETED = auto()
    ERROR = auto()
    RUNNING = auto()
    PAUSED = auto()
    STOPPED = auto()
```

## 回调函数

### GUI回调函数
```python
def gui_callback(tab_id: int, message_type: str, data: Any):
    """
    GUI回调函数
    
    Args:
        tab_id: 标签页ID
        message_type: 消息类型 ('status', 'progress', 'data')
        data: 消息数据
    """
```

## 使用示例

### 基本使用
```python
from gui_scraper import XueqiuScraperTab, ScraperConfig

# 创建配置
config = ScraperConfig()

# 定义回调函数
def callback(tab_id, msg_type, data):
    print(f"Tab {tab_id}: {msg_type} - {data}")

# 创建爬虫实例
scraper = XueqiuScraperTab(
    tab_id=1,
    keyword="股票",
    max_items=10,
    gui_callback=callback,
    config=config
)

# 开始爬取
scraper.start_scraping()
```

### 配置管理
```python
from gui_scraper import ConfigManager

# 创建配置管理器
config_manager = ConfigManager()

# 修改配置
config_manager.config["max_scrolls"] = 30

# 保存配置
config_manager.save_config()

# 获取爬虫配置
scraper_config = config_manager.get_scraper_config()
```
