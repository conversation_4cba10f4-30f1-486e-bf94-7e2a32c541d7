"""
测试ChromeDriver和Selenium集成
"""

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
import os

def test_chromedriver():
    """测试ChromeDriver是否能正常工作"""
    try:
        # 配置Chrome选项
        options = Options()
        options.add_argument('--headless')  # 无头模式
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--window-size=1200,800')
        
        # ChromeDriver路径
        driver_path = r'd:/myCursor/XDB3/chromedriver/chromedriver.exe'
        
        if not os.path.exists(driver_path):
            print(f"ChromeDriver路径不存在: {driver_path}")
            return False
        
        # 创建服务
        service = Service(driver_path)
        
        # 创建浏览器实例
        print("正在启动Chrome浏览器...")
        driver = webdriver.Chrome(service=service, options=options)
        
        # 测试访问网页
        print("正在访问测试页面...")
        driver.get("https://www.baidu.com")
        
        # 获取页面标题
        title = driver.title
        print(f"页面标题: {title}")
        
        # 关闭浏览器
        driver.quit()
        
        print("✅ ChromeDriver测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ ChromeDriver测试失败: {e}")
        return False

if __name__ == "__main__":
    test_chromedriver()
