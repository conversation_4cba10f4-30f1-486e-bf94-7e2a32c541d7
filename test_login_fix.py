"""
简单测试登录修复效果
"""

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
import time
import os

def test_browser_persistence():
    """测试浏览器是否会自动关闭"""
    print("🌐 测试浏览器持久性...")
    
    try:
        # 配置Chrome选项 - 使用修复后的配置
        options = Options()
        
        # 基本稳定性选项
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--window-size=1200,800')
        options.add_argument('--disable-blink-features=AutomationControlled')
        
        # 使用独立的用户数据目录
        options.add_argument('--user-data-dir=test_chrome_profile')
        
        # 使用动态端口
        options.add_argument('--remote-debugging-port=9223')
        
        # 防止浏览器自动关闭的关键选项
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-popup-blocking')
        
        # 保持浏览器活跃的选项
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-features=TranslateUI')
        options.add_argument('--disable-ipc-flooding-protection')
        
        # 网络和安全选项
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-ssl-errors')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--disable-web-security')
        
        # 禁用可能导致崩溃的功能
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-gpu')
        
        # 禁用日志和自动化检测
        options.add_experimental_option('excludeSwitches', ['enable-logging', 'enable-automation'])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument('--disable-logging')
        options.add_argument('--log-level=3')
        
        # 关键：添加保持浏览器打开的选项
        options.add_experimental_option("detach", True)
        
        # 检查ChromeDriver路径
        driver_paths = [
            r'd:/myCursor/XDB3/chromedriver/chromedriver.exe',
            r'D:/myCursor/XDB3/chromedriver/chromedriver-win64/chromedriver.exe'
        ]
        
        driver_path = None
        for path in driver_paths:
            if os.path.exists(path):
                driver_path = path
                break
        
        if not driver_path:
            print("❌ ChromeDriver路径未找到")
            return False
        
        print(f"使用ChromeDriver: {driver_path}")
        
        # 创建服务和浏览器
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=options)
        
        # 设置超时
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(10)
        
        # 隐藏webdriver特征
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ 浏览器创建成功")
        
        # 打开雪球网站
        print("正在打开雪球网站...")
        driver.get("https://xueqiu.com/")
        
        # 等待页面加载
        WebDriverWait(driver, 30).until(
            lambda d: d.execute_script('return document.readyState') == 'complete'
        )
        
        print("✅ 雪球网站加载成功")
        print("🔑 请在浏览器中完成登录操作")
        print("💡 浏览器应该保持打开状态，不会自动关闭")
        
        # 监控浏览器状态60秒
        print("\n监控浏览器状态60秒...")
        for i in range(60):
            try:
                # 检查浏览器是否还活着
                current_url = driver.current_url
                if i % 10 == 0:
                    print(f"   {i+1}/60秒 - 浏览器状态正常，当前URL: {current_url[:50]}...")
                time.sleep(1)
            except Exception as e:
                print(f"❌ 浏览器在 {i+1} 秒后断开连接: {e}")
                return False
        
        print("✅ 浏览器保持打开60秒，修复成功！")
        
        # 手动关闭（用户可以选择不关闭）
        try:
            user_input = input("\n按Enter键关闭浏览器，或Ctrl+C保持打开: ")
            driver.quit()
            print("✅ 浏览器已关闭")
        except KeyboardInterrupt:
            print("\n💡 浏览器保持打开状态")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 浏览器自动关闭问题修复测试\n")
    
    if test_browser_persistence():
        print("\n🎉 修复验证成功！浏览器不再自动关闭")
    else:
        print("\n❌ 修复验证失败，可能需要进一步调整")
