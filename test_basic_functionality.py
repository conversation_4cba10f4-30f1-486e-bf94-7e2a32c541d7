"""
测试雪球爬虫的基本功能
"""

import sys
import os
import time
from gui_scraper import (
    ScraperConfig, 
    XueqiuScraperTab, 
    BrowserFactory,
    ConfigManager
)

def test_config_manager():
    """测试配置管理器"""
    print("🔧 测试配置管理器...")
    try:
        config_manager = ConfigManager()
        scraper_config = config_manager.get_scraper_config()
        print(f"✅ 配置加载成功")
        print(f"   ChromeDriver路径: {scraper_config.chrome_driver_path}")
        print(f"   最大滚动次数: {scraper_config.max_scrolls}")
        return True
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_browser_factory():
    """测试浏览器工厂"""
    print("\n🌐 测试浏览器工厂...")
    try:
        config = ScraperConfig()
        config.headless = True  # 使用无头模式
        
        browser = BrowserFactory.create_browser(config)
        if browser:
            print("✅ 浏览器创建成功")
            
            # 测试基本操作
            browser.get("https://www.baidu.com")
            title = browser.title
            print(f"   测试页面标题: {title}")
            
            browser.quit()
            print("✅ 浏览器关闭成功")
            return True
        else:
            print("❌ 浏览器创建失败")
            return False
    except Exception as e:
        print(f"❌ 浏览器工厂测试失败: {e}")
        return False

def test_scraper_initialization():
    """测试爬虫初始化"""
    print("\n🕷️ 测试爬虫初始化...")
    try:
        config = ScraperConfig()
        config.headless = True
        
        def dummy_callback(tab_id, message_type, data):
            print(f"   回调消息: {message_type} - {data}")
        
        scraper = XueqiuScraperTab(
            tab_id=1,
            keyword="测试",
            max_items=5,
            gui_callback=dummy_callback,
            config=config
        )
        
        print("✅ 爬虫实例创建成功")
        print(f"   标签页ID: {scraper.tab_id}")
        print(f"   关键字: {scraper.keyword}")
        print(f"   最大项目数: {scraper.max_items}")
        
        return True
    except Exception as e:
        print(f"❌ 爬虫初始化测试失败: {e}")
        return False

def test_chromedriver_path_detection():
    """测试ChromeDriver路径检测"""
    print("\n🔍 测试ChromeDriver路径检测...")
    try:
        config = ScraperConfig()
        
        def dummy_callback(tab_id, message_type, data):
            pass
        
        scraper = XueqiuScraperTab(
            tab_id=1,
            keyword="测试",
            max_items=5,
            gui_callback=dummy_callback,
            config=config
        )
        
        # 测试路径查找功能
        found_path = scraper._find_chromedriver_path()
        if found_path:
            print(f"✅ ChromeDriver路径检测成功: {found_path}")
            return True
        else:
            print("❌ ChromeDriver路径检测失败")
            return False
    except Exception as e:
        print(f"❌ ChromeDriver路径检测测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始基本功能测试...\n")
    
    tests = [
        ("配置管理器", test_config_manager),
        ("浏览器工厂", test_browser_factory),
        ("爬虫初始化", test_scraper_initialization),
        ("ChromeDriver路径检测", test_chromedriver_path_detection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基本功能测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
